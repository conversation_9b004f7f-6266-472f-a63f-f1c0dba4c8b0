// Modern Admin Panel JavaScript - Extracted for better maintainability
class ModernAdminPanel {
  constructor(initialProducts) {
    this.products = [...initialProducts];
    this.filteredProducts = [...this.products];
    this.categories = this.getCategories();
    this.currentPage = 1;
    this.itemsPerPage = 20;
    this.currentFilter = '';
    this.currentSearch = '';
    this.currentSort = 'name';
    this.editingProduct = null;
    this.editingCategory = null;
    this.keyPoints = [];

    // Track unsaved changes
    this.hasUnsavedChanges = false;
    this.lastSyncedProducts = [...initialProducts];

    this.init();
  }
  
  init() {
    this.checkAdminAccess();
    this.bindEvents();
    this.renderStats();
    this.renderProducts();
    this.initializeForm();
    this.checkGitHubStatus();
    this.updateSyncButtonState();
  }
  
  checkAdminAccess() {
    const isAdmin = new URLSearchParams(window.location.search).get('admin') === '1';
    const adminContent = document.getElementById('admin-content');
    const notAdmin = document.getElementById('not-admin');
    
    if (isAdmin) {
      adminContent.style.display = 'block';
      notAdmin.style.display = 'none';
    } else {
      adminContent.style.display = 'none';
      notAdmin.style.display = 'block';
    }
  }

  bindEvents() {
    // Tab navigation
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.target.closest('.admin-tab-btn').dataset.tab;
        this.switchTab(tab);
      });
    });

    // Filters and search
    document.getElementById('admin-category-filter').addEventListener('change', (e) => {
      this.currentFilter = e.target.value;
      this.currentPage = 1;
      this.filterAndRenderProducts();
    });

    document.getElementById('admin-sort-by').addEventListener('change', (e) => {
      this.currentSort = e.target.value;
      this.filterAndRenderProducts();
    });

    document.getElementById('admin-search').addEventListener('input', (e) => {
      this.currentSearch = e.target.value.toLowerCase();
      this.currentPage = 1;
      this.filterAndRenderProducts();
    });

    // Product actions
    document.getElementById('add-product-btn').addEventListener('click', () => {
      this.editingProduct = null;
      this.switchTab('form');
      this.resetForm();
    });

    document.getElementById('save-product').addEventListener('click', () => {
      this.saveProduct();
    });

    document.getElementById('cancel-form').addEventListener('click', () => {
      this.switchTab('list');
      this.resetForm();
    });

    // Category actions
    document.getElementById('add-category-btn').addEventListener('click', () => {
      this.showCategoryForm();
    });

    document.getElementById('save-category').addEventListener('click', () => {
      this.saveCategory();
    });

    document.getElementById('cancel-category').addEventListener('click', () => {
      this.hideCategoryForm();
    });

    // Sync actions
    document.getElementById('sync-products').addEventListener('click', () => {
      this.syncProducts();
    });

    document.getElementById('test-github').addEventListener('click', () => {
      this.testGitHub();
    });
  }

  switchTab(tab) {
    // Update tab buttons
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

    // Update sections
    document.querySelectorAll('.admin-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(`section-${tab}`).classList.add('active');

    if (tab === 'form') {
      this.initializeForm();
    } else if (tab === 'categories') {
      this.renderCategories();
    }
  }

  getCategories() {
    const categories = [...new Set(this.products.map(p => p.category))].filter(Boolean);
    return categories.sort();
  }

  renderStats() {
    document.getElementById('total-products').textContent = this.products.length;
    document.getElementById('visible-products').textContent = this.filteredProducts.length;
    document.getElementById('categories-count').textContent = this.categories.length;
  }

  filterAndRenderProducts() {
    let filtered = [...this.products];

    // Apply category filter
    if (this.currentFilter) {
      filtered = filtered.filter(p => p.category === this.currentFilter);
    }

    // Apply search filter
    if (this.currentSearch) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(this.currentSearch) ||
        p.description.toLowerCase().includes(this.currentSearch) ||
        p.category.toLowerCase().includes(this.currentSearch)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.currentSort) {
        case 'price':
          return parseFloat(a.price) - parseFloat(b.price);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'newest':
          return new Date(b.dateAdded || 0) - new Date(a.dateAdded || 0);
        default: // name
          return a.name.localeCompare(b.name);
      }
    });

    this.filteredProducts = filtered;
    this.renderProducts();
    this.renderStats();
  }

  renderProducts() {
    const tbody = document.getElementById('products-tbody');
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

    if (pageProducts.length === 0) {
      document.getElementById('empty-state').style.display = 'block';
      document.querySelector('.admin-table-wrapper').style.display = 'none';
      document.getElementById('pagination').style.display = 'none';
    } else {
      document.getElementById('empty-state').style.display = 'none';
      document.querySelector('.admin-table-wrapper').style.display = 'block';
      document.getElementById('pagination').style.display = 'flex';
    }

    tbody.innerHTML = pageProducts.map(product => this.renderProductRow(product)).join('');
    this.renderPagination();
  }

  renderProductRow(product) {
    const isNew = !this.lastSyncedProducts.find(p => p.id === product.id);
    const isModified = this.lastSyncedProducts.find(p => 
      p.id === product.id && JSON.stringify(p) !== JSON.stringify(product)
    );
    
    const rowClass = isNew ? 'product-new' : (isModified ? 'product-modified' : '');
    const changeIndicator = isNew ? '🆕' : (isModified ? '📝' : '');
    
    return `
      <tr class="product-row ${rowClass}">
        <td class="product-name-cell">
          <div class="product-name-container">
            <span class="change-indicator">${changeIndicator}</span>
            <strong>${product.name}</strong>
            <div class="product-description-preview">${product.description.substring(0, 100)}...</div>
          </div>
        </td>
        <td><span class="category-badge">${product.category}</span></td>
        <td>$${parseFloat(product.price).toFixed(2)}</td>
        <td class="images-count">${product.images ? product.images.length : 0} images</td>
        <td>
          <div class="status-indicators">
            <span class="status-badge good">Good</span>
            ${product.defects ? '<span class="status-badge defects">Defects</span>' : ''}
          </div>
        </td>
        <td>
          <div class="action-buttons">
            <button class="btn-edit" onclick="adminPanel.editProduct('${product.id}')" title="Edit">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </button>
            <button class="btn-delete" onclick="adminPanel.deleteProduct('${product.id}')" title="Delete">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
            </button>
          </div>
        </td>
      </tr>
    `;
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';
    
    // Previous button
    if (this.currentPage > 1) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage - 1})">‹</button>`;
    }
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
      if (i === this.currentPage) {
        paginationHTML += `<button class="page-btn active">${i}</button>`;
      } else {
        paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${i})">${i}</button>`;
      }
    }
    
    // Next button
    if (this.currentPage < totalPages) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage + 1})">›</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
  }

  goToPage(page) {
    this.currentPage = page;
    this.renderProducts();
  }

  initializeForm() {
    // Initialize the product form with all necessary fields
    const form = document.getElementById('product-form');
    if (!form) return;

    form.innerHTML = `
      <div class="professional-form-wrapper">
        <div class="form-header-section">
          <div class="form-header-content">
            <div class="form-header-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
              </svg>
            </div>
            <div class="form-header-text">
              <h2>Product Information</h2>
              <p>Fill in the details for your product listing</p>
            </div>
          </div>
        </div>

        <div class="form-content-grid">
          <div class="form-card primary-card">
            <div class="card-header">
              <div class="card-icon primary-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              <div class="card-title">
                <h3>Basic Information</h3>
                <p>Essential product details</p>
              </div>
            </div>
            <div class="card-content">
              <div class="input-group">
                <label for="product-name" class="input-label">Product Name <span class="required">*</span></label>
                <input type="text" id="product-name" name="name" required class="form-input" placeholder="Enter product name" />
              </div>

              <div class="input-group">
                <label for="product-description" class="input-label">Description <span class="required">*</span></label>
                <textarea id="product-description" name="description" required class="form-textarea" placeholder="Describe your product in detail..."></textarea>
              </div>

              <div class="input-row">
                <div class="input-group">
                  <label for="product-category" class="input-label">Category <span class="required">*</span></label>
                  <select id="product-category" name="category" required class="form-select">
                    <option value="">Select a category</option>
                    <option value="Clothing">Clothing</option>
                    <option value="Books">Books</option>
                    <option value="Arts & Crafts">Arts & Crafts</option>
                    <option value="Home Decor">Home Decor</option>
                    <option value="Electronics">Electronics</option>
                    <option value="Toys & Games">Toys & Games</option>
                  </select>
                </div>

                <div class="input-group">
                  <label for="product-price" class="input-label">Price <span class="required">*</span></label>
                  <div class="price-input-wrapper">
                    <span class="currency-prefix">$</span>
                    <input type="number" id="product-price" name="price" required class="form-input price-input" placeholder="0.00" step="0.01" min="0" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-card">
            <div class="card-header">
              <div class="card-icon secondary-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9A3,3 0 0,0 9,12M12,17L18.36,10.64C21.15,7.85 21.15,3.15 18.36,0.36C15.57,-2.43 10.87,-2.43 8.08,0.36C5.29,3.15 5.29,7.85 8.08,10.64L12,17Z"/>
                </svg>
              </div>
              <div class="card-title">
                <h3>Images</h3>
                <p>Product photos (URLs)</p>
              </div>
            </div>
            <div class="card-content">
              <div class="input-group image-upload-area">
                <label for="product-images" class="input-label">Image URLs</label>
                <textarea id="product-images" name="images" class="form-textarea image-textarea" placeholder="Enter image URLs, one per line..." rows="6"></textarea>
                <div class="input-help">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
                  </svg>
                  Enter one image URL per line. Images should be publicly accessible.
                </div>
              </div>
            </div>
          </div>

          <div class="form-card">
            <div class="card-header">
              <div class="card-icon features-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,2A2,2 0 0,1 14,4V8A2,2 0 0,1 12,10A2,2 0 0,1 10,8V4A2,2 0 0,1 12,2M21,9V7H19V9H21M17,9V7H15V9H17M13,9V7H11V9H13M9,9V7H7V9H9M5,9V7H3V9H5Z"/>
                </svg>
              </div>
              <div class="card-title">
                <h3>Key Features</h3>
                <p>Highlight important features</p>
              </div>
            </div>
            <div class="card-content">
              <div class="features-display-area" id="features-display">
                <!-- Key points will be displayed here -->
              </div>

              <div class="features-input-section">
                <div class="features-input-grid">
                  <div class="input-group">
                    <label for="feature-label" class="input-label">Feature Label</label>
                    <input type="text" id="feature-label" class="form-input" placeholder="e.g., Material" />
                  </div>
                  <div class="input-group">
                    <label for="feature-value" class="input-label">Feature Value</label>
                    <input type="text" id="feature-value" class="form-input" placeholder="e.g., 100% Cotton" />
                  </div>
                </div>
                <button type="button" id="add-feature" class="add-feature-button">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  </svg>
                  Add Feature
                </button>
              </div>
            </div>
          </div>

          <div class="form-card">
            <div class="card-header">
              <div class="card-icon notes-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,10H19.5L14,4.5V10M5,3H15L21,9V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3M9,12H16V14H9V12M9,16H14V18H9V16Z"/>
                </svg>
              </div>
              <div class="card-title">
                <h3>Additional Notes</h3>
                <p>Defects, condition notes</p>
              </div>
            </div>
            <div class="card-content">
              <div class="input-group">
                <label for="product-defects" class="input-label">Defects/Condition Notes</label>
                <textarea id="product-defects" name="defects" class="form-textarea" placeholder="Describe any defects, wear, or condition issues..." rows="4"></textarea>
                <div class="input-help warning-help">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/>
                  </svg>
                  Be honest about any defects to maintain customer trust.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Bind form events
    this.bindFormEvents();
  }

  bindFormEvents() {
    // Add feature functionality
    const addFeatureBtn = document.getElementById('add-feature');
    if (addFeatureBtn) {
      addFeatureBtn.addEventListener('click', () => {
        this.addKeyPoint();
      });
    }

    // Enter key support for adding features
    const featureLabel = document.getElementById('feature-label');
    const featureValue = document.getElementById('feature-value');

    if (featureLabel && featureValue) {
      [featureLabel, featureValue].forEach(input => {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            this.addKeyPoint();
          }
        });
      });
    }
  }

  // Custom styled confirmation dialog
  showConfirmDialog(title, message, details, confirmText = 'Confirm', type = 'danger') {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.className = 'confirm-modal-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(4px);
      `;

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'confirm-modal';
      modal.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        transition: transform 0.2s ease;
      `;

      const typeColors = {
        danger: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
      };

      modal.innerHTML = `
        <div class="confirm-header" style="margin-bottom: 1rem;">
          <h3 style="margin: 0; color: #0f172a; font-size: 1.25rem; font-weight: 600;">${title}</h3>
        </div>
        <div class="confirm-body" style="margin-bottom: 2rem;">
          <p style="margin: 0 0 0.5rem 0; color: #374151; font-size: 1rem;">${message}</p>
          ${details ? `<p style="margin: 0; color: #6b7280; font-size: 0.875rem;">${details}</p>` : ''}
        </div>
        <div class="confirm-actions" style="display: flex; gap: 0.75rem; justify-content: flex-end;">
          <button class="confirm-cancel" style="
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
          ">Cancel</button>
          <button class="confirm-action" style="
            padding: 0.5rem 1rem;
            border: none;
            background: ${typeColors[type] || typeColors.danger};
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
          ">${confirmText}</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Animate in
      requestAnimationFrame(() => {
        modal.style.transform = 'scale(1)';
      });

      // Event handlers
      const cleanup = () => {
        overlay.style.opacity = '0';
        modal.style.transform = 'scale(0.95)';
        setTimeout(() => {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        }, 200);
      };

      modal.querySelector('.confirm-cancel').addEventListener('click', () => {
        cleanup();
        resolve(false);
      });

      modal.querySelector('.confirm-action').addEventListener('click', () => {
        cleanup();
        resolve(true);
      });

      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          cleanup();
          resolve(false);
        }
      });

      // ESC key support
      const handleEsc = (e) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleEsc);
        }
      };
      document.addEventListener('keydown', handleEsc);
    });
  }

  addKeyPoint() {
    const labelInput = document.getElementById('feature-label');
    const valueInput = document.getElementById('feature-value');

    if (!labelInput || !valueInput) return;

    const label = labelInput.value.trim();
    const value = valueInput.value.trim();

    if (label && value) {
      this.keyPoints.push({ label, value });
      this.renderKeyPoints();
      labelInput.value = '';
      valueInput.value = '';
      labelInput.focus();
    }
  }

  renderKeyPoints() {
    const container = document.getElementById('features-display');
    if (!container) return;

    container.innerHTML = this.keyPoints.map((point, index) => `
      <div class="keypoint-item">
        <span class="keypoint-label">${point.label}:</span>
        <span class="keypoint-value">${point.value}</span>
        <button type="button" class="btn-remove-keypoint" onclick="adminPanel.removeKeyPoint(${index})" title="Remove">×</button>
      </div>
    `).join('');
  }

  removeKeyPoint(index) {
    this.keyPoints.splice(index, 1);
    this.renderKeyPoints();
  }

  editProduct(productId) {
    const product = this.products.find(p => p.id === productId);
    if (!product) return;

    this.editingProduct = product;
    this.switchTab('form');
    this.populateForm(product);
    document.getElementById('form-title').textContent = 'Edit Product';
  }

  populateForm(product) {
    // Populate basic fields
    document.getElementById('product-name').value = product.name || '';
    document.getElementById('product-description').value = product.description || '';
    document.getElementById('product-category').value = product.category || '';
    document.getElementById('product-price').value = product.price || '';
    document.getElementById('product-defects').value = product.defects || '';

    // Populate images
    if (product.images && product.images.length > 0) {
      document.getElementById('product-images').value = product.images.join('\n');
    }

    // Populate key points
    this.keyPoints = [...(product.keyPoints || [])];
    this.renderKeyPoints();
  }

  resetForm() {
    this.editingProduct = null;
    this.keyPoints = [];
    document.getElementById('form-title').textContent = 'Add New Product';

    // Reset all form fields
    const form = document.getElementById('product-form');
    if (form) {
      const inputs = form.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.value = '';
      });
    }

    this.renderKeyPoints();
  }

  saveProduct() {
    const form = document.getElementById('product-form');
    if (!form) return;

    // Get form data
    const formData = new FormData(form);
    const productData = {
      name: formData.get('name')?.trim(),
      description: formData.get('description')?.trim(),
      category: formData.get('category'),
      price: parseFloat(formData.get('price')) || 0,
      defects: formData.get('defects')?.trim() || '',
      images: formData.get('images')?.split('\n').filter(url => url.trim()).map(url => url.trim()) || [],
      keyPoints: [...this.keyPoints],
      dateAdded: new Date().toISOString()
    };

    // Validate required fields
    if (!productData.name || !productData.description || !productData.category || !productData.price) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please fill in all required fields: Name, Description, Category, and Price.',
          'error',
          4000
        );
      }
      return;
    }

    if (this.editingProduct) {
      // Update existing product
      productData.id = this.editingProduct.id;
      const index = this.products.findIndex(p => p.id === this.editingProduct.id);
      if (index !== -1) {
        this.products[index] = productData;
      }
    } else {
      // Add new product
      productData.id = 'product-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      this.products.push(productData);
    }

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.switchTab('list');
    this.resetForm();
    this.filterAndRenderProducts();

    // Show success message
    if (window.showToast) {
      window.showToast(
        this.editingProduct ? 'Product Updated' : 'Product Added',
        this.editingProduct ? 'Product updated successfully!' : 'Product added successfully!',
        'success',
        3000
      );
    }
  }

  deleteProduct(productId) {
    // Create a custom confirmation dialog instead of using alert
    const product = this.products.find(p => p.id === productId);
    const productName = product ? product.name : 'this product';

    // Use custom styled confirmation dialog
    const confirmed = await this.showConfirmDialog(
      'Delete Product',
      `Are you sure you want to delete "${productName}"?`,
      'This action cannot be undone and will remove the product from your catalog.',
      'Delete Product',
      'danger'
    );

    if (!confirmed) return;

    const index = this.products.findIndex(p => p.id === productId);
    if (index !== -1) {
      this.products.splice(index, 1);
      this.hasUnsavedChanges = true;
      this.updateSyncButtonState();
      this.filterAndRenderProducts();

      // Show success toast
      if (window.showToast) {
        window.showToast(
          'Product Deleted',
          `"${productName}" has been deleted successfully.`,
          'warning',
          3000
        );
      }
    }
  }

  renderCategories() {
    const grid = document.getElementById('categories-grid');
    if (!grid) return;

    const categories = this.getCategories();

    if (categories.length === 0) {
      grid.innerHTML = '<div class="no-categories">No categories found. Add your first category to get started.</div>';
      return;
    }

    grid.innerHTML = categories.map(category => `
      <div class="category-card">
        <div class="category-info">
          <h4>${category}</h4>
          <p>${this.products.filter(p => p.category === category).length} products</p>
        </div>
        <div class="category-actions">
          <button class="btn-edit-category" onclick="adminPanel.editCategory('${category}')" title="Edit">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
            </svg>
          </button>
          <button class="btn-delete-category" onclick="adminPanel.deleteCategory('${category}')" title="Delete">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
            </svg>
          </button>
        </div>
      </div>
    `).join('');
  }

  showCategoryForm() {
    const container = document.getElementById('category-form-container');
    if (container) {
      container.style.display = 'block';
      document.getElementById('category-name').focus();
    }
  }

  hideCategoryForm() {
    const container = document.getElementById('category-form-container');
    if (container) {
      container.style.display = 'none';
      document.getElementById('category-form').reset();
      this.editingCategory = null;
    }
  }

  saveCategory() {
    const name = document.getElementById('category-name').value.trim();
    const description = document.getElementById('category-description').value.trim();

    if (!name) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please enter a category name.',
          'error',
          4000
        );
      }
      return;
    }

    // For now, just hide the form and refresh categories
    // In a real app, you'd save this to your data store
    this.hideCategoryForm();
    this.renderCategories();

    if (window.showToast) {
      window.showToast(
        'Feature Not Available',
        'Category functionality is not fully implemented in this demo.',
        'warning',
        4000
      );
    }
  }

  editCategory(categoryName) {
    this.editingCategory = categoryName;
    document.getElementById('category-name').value = categoryName;
    document.getElementById('category-form-title').textContent = 'Edit Category';
    this.showCategoryForm();
  }

  async deleteCategory(categoryName) {
    const confirmed = await this.showConfirmDialog(
      'Delete Category',
      `Are you sure you want to delete the "${categoryName}" category?`,
      'This action cannot be undone.',
      'Delete Category',
      'danger'
    );

    if (!confirmed) return;

    // In a real app, you'd handle category deletion properly
    if (window.showToast) {
      window.showToast(
        'Feature Not Available',
        'Category deletion is not fully implemented in this demo.',
        'warning',
        4000
      );
    }
  }

  updateSyncButtonState() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    if (this.hasUnsavedChanges) {
      syncBtn.classList.add('has-changes');
      syncBtn.title = 'You have unsaved changes. Click to sync and deploy.';
    } else {
      syncBtn.classList.remove('has-changes');
      syncBtn.title = 'Sync with server and deploy';
    }
  }

  async checkGitHubStatus() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    // Simulate GitHub status check
    indicator.textContent = '🟡';
    text.textContent = 'Checking...';

    setTimeout(() => {
      indicator.textContent = '🟢';
      text.textContent = 'Connected';
    }, 1000);
  }

  async testGitHub() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    indicator.textContent = '🟡';
    text.textContent = 'Testing...';

    try {
      // Call the real GitHub test API
      const response = await fetch('/api/github-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test-commit'
        })
      });

      const result = await response.json();
      console.log('GitHub test result:', result);

      if (result.success) {
        indicator.textContent = '🟢';
        text.textContent = 'Test successful';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Successful',
            `Repository: ${result.repoInfo?.full_name || 'Connected'}\nCommit SHA: ${result.commitSha || 'N/A'}`,
            'success',
            5000
          );
        }
      } else {
        indicator.textContent = '🔴';
        text.textContent = 'Test failed';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Failed',
            `${result.error || 'Unknown error'}\n\nPlease check your GitHub configuration.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('GitHub test error:', error);
      indicator.textContent = '🔴';
      text.textContent = 'Test failed';
      if (window.showToast) {
        window.showToast(
          'GitHub Test Error',
          `${error.message}\n\nPlease check your internet connection and GitHub configuration.`,
          'error',
          6000
        );
      }
    }
  }

  async syncProducts() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    const originalHTML = syncBtn.innerHTML;
    syncBtn.innerHTML = '<div class="spinner-small"></div> Syncing...';
    syncBtn.disabled = true;

    try {
      console.log('Starting sync process...');
      console.log('Products to sync:', this.products.length);
      console.log('Product IDs:', this.products.map(p => p.id));

      // Call the real sync API endpoint
      const response = await fetch('/api/sync-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          products: this.products
        })
      });

      const result = await response.json();
      console.log('Sync API response:', result);

      if (result.success) {
        this.hasUnsavedChanges = false;
        this.lastSyncedProducts = [...this.products];
        this.updateSyncButtonState();

        if (window.showToast) {
          // Extract the correct values from the API response
          const productsCount = result.count || this.products.length;
          const githubCommitted = result.github?.committed || false;
          const githubCommitSha = result.github?.commitSha;

          // Check if cache was purged (we need to add this to the API response)
          const cachePurged = result.cache?.purged || false;

          let details = `• ${productsCount} products synced\n`;
          details += `• GitHub commit: ${githubCommitted ? 'Success' : 'Skipped'}`;
          if (githubCommitted && githubCommitSha) {
            details += ` (${githubCommitSha.substring(0, 7)})`;
          }
          details += `\n• Cache purged: ${cachePurged ? 'Yes' : 'No'}`;

          window.showToast(
            'Sync Successful',
            `Products synced and deployed successfully!\n\n${details}`,
            'success',
            6000
          );
        }
        this.renderProducts(); // Refresh to remove change indicators
      } else {
        console.error('Sync failed:', result.error);
        if (window.showToast) {
          window.showToast(
            'Sync Failed',
            `${result.error || 'Unknown error'}\n\nPlease check the console for more details and try again.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('Sync error:', error);
      if (window.showToast) {
        window.showToast(
          'Sync Error',
          `${error.message}\n\nPlease check your internet connection and try again.`,
          'error',
          6000
        );
      }
    } finally {
      syncBtn.innerHTML = originalHTML;
      syncBtn.disabled = false;
    }
  }
}

// Export for use in other modules
window.ModernAdminPanel = ModernAdminPanel;
